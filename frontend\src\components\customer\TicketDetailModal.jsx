import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  MessageCircle,
  Trash2,
  User,
  X,
  XCircle,
} from "lucide-react";
import CommentSection from "./CommentSection";

const TicketDetailModal = ({ ticket, isOpen, onClose, onTicketAction }) => {
  if (!isOpen || !ticket) return null;

  // Determine what actions are available for this ticket
  const canComment = ["In Progress", "Approved", "Resolved", "Seen"].includes(
    ticket.status
  );
  const canClose =
    ticket.canClose &&
    ["In Progress", "Approved", "Seen"].includes(ticket.status);
  const canDelete = ticket.canDelete;
  const canResubmit = ticket.canResubmit && ticket.status === "Rejected";
  const showRejectionReason =
    ticket.status === "Rejected" && ticket.rejectionReason;

  const getStatusIcon = (status) => {
    switch (status) {
      case "Pending Approval":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "In Progress":
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      case "Resolved":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "Rejected":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending Approval":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "bg-red-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-gray-900">
              {ticket.id}
            </h2>
            <div className="flex items-center">
              {getStatusIcon(ticket.status)}
              <span
                className={`ml-2 px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                  ticket.status
                )}`}
              >
                {ticket.status}
              </span>
            </div>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            title="Close Modal"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Three Column Content */}
        <div className="flex-1 overflow-hidden flex bg-gray-50">
          {/* Left Column - Ticket Details */}
          <div className="w-1/4 bg-white border-r border-gray-200 overflow-y-auto shadow-sm">
            <div className="p-3">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                  <User className="w-4 h-4 text-blue-600" />
                </div>
                <h3 className="text-sm font-semibold text-gray-900">
                  Ticket Details
                </h3>
              </div>

              {/* Compact Information Cards */}
              <div className="space-y-2 mb-4">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-2 border border-blue-100">
                  <label className="block text-xs font-medium text-blue-700 mb-1">
                    Subject
                  </label>
                  <p className="text-xs text-gray-900 font-medium leading-tight">
                    {ticket.subject}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="bg-gray-50 rounded-md p-2 border">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Category
                    </label>
                    <p className="text-xs text-gray-900">{ticket.category}</p>
                  </div>
                  <div className="bg-gray-50 rounded-md p-2 border">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Priority
                    </label>
                    <div className="flex items-center">
                      <div
                        className={`w-2 h-2 rounded-full mr-1 ${getPriorityColor(
                          ticket.priority
                        )}`}
                      ></div>
                      <span className="text-xs text-gray-900">
                        {ticket.priority}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-2 border border-green-100">
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-green-700 mb-1">
                        Created
                      </label>
                      <div className="flex items-center text-xs text-gray-900">
                        <Calendar className="w-3 h-3 mr-1" />
                        {ticket.created}
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-green-700 mb-1">
                        Agent
                      </label>
                      <div className="flex items-center text-xs text-gray-900">
                        <User className="w-3 h-3 mr-1" />
                        {ticket.agent || "Unassigned"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="mb-3">
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Description
                </label>
                <div className="bg-gray-50 rounded-md p-2 border max-h-20 overflow-y-auto">
                  <p className="text-xs text-gray-900 leading-relaxed">
                    {ticket.description || "No description provided."}
                  </p>
                </div>
              </div>

              {/* Rejection Reason */}
              {showRejectionReason && (
                <div className="mb-3">
                  <div className="bg-red-50 border border-red-200 rounded-md p-2">
                    <div className="flex items-start">
                      <AlertCircle className="w-3 h-3 text-red-500 mt-0.5 mr-1 flex-shrink-0" />
                      <div>
                        <label className="block text-xs font-medium text-red-700 mb-1">
                          Rejection Reason
                        </label>
                        <p className="text-xs text-red-800 leading-tight">
                          {ticket.rejectionReason}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Approval Notes */}
              {ticket.approvalNotes && (
                <div className="mb-3">
                  <div className="bg-green-50 border border-green-200 rounded-md p-2">
                    <div className="flex items-start">
                      <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 mr-1 flex-shrink-0" />
                      <div>
                        <label className="block text-xs font-medium text-green-700 mb-1">
                          Approval Notes
                        </label>
                        <p className="text-xs text-green-800 leading-tight">
                          {ticket.approvalNotes}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Middle Column - Chat (Larger) */}
          <div className="w-3/4 bg-white flex flex-col shadow-sm">
            <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-teal-50 to-cyan-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                    <MessageCircle className="w-4 h-4 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-gray-900">
                      Communication
                    </h3>
                    <p className="text-xs text-gray-600">
                      Chat with support team
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  {canResubmit && (
                    <button
                      onClick={() => onTicketAction("resubmit", ticket)}
                      className="flex items-center px-3 py-1.5 text-xs bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-md hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-sm"
                    >
                      <Edit className="w-3 h-3 mr-1" />
                      Resubmit
                    </button>
                  )}

                  {canDelete && (
                    <button
                      onClick={() => onTicketAction("delete", ticket)}
                      className="flex items-center px-3 py-1.5 text-xs bg-gradient-to-r from-red-500 to-rose-600 text-white rounded-md hover:from-red-600 hover:to-rose-700 transition-all duration-200 shadow-sm"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Delete
                    </button>
                  )}

                  {canClose && (
                    <button
                      onClick={() => onTicketAction("close", ticket)}
                      className="flex items-center px-3 py-1.5 text-xs bg-gradient-to-r from-red-500 to-rose-600 text-white rounded-md hover:from-red-600 hover:to-rose-700 transition-all duration-200 shadow-sm"
                    >
                      <X className="w-3 h-3 mr-1" />
                      Close Ticket
                    </button>
                  )}
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              {canComment ? (
                <CommentSection
                  ticketId={ticket.id}
                  canComment={canComment}
                  ticketStatus={ticket.status}
                />
              ) : (
                <div className="p-6 h-full flex items-center justify-center bg-gradient-to-br from-yellow-50 to-orange-50">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <AlertCircle className="w-8 h-8 text-yellow-600" />
                    </div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      Waiting for Approval
                    </h4>
                    <p className="text-xs text-gray-600 max-w-xs">
                      Comments can only be added after the ticket has been
                      approved by an agent.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicketDetailModal;
