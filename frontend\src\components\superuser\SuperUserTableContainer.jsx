import SuperUserTableHeaders from "./SuperUserTableHeaders";
import SuperUserTableRow from "./SuperUserTableRow";

const SuperUserTableContainer = ({
  activeView,
  currentViewTickets,
  selectedTickets,
  activeFilterColumn,
  pendingFilters,
  onSelectAllTickets,
  onFilterColumnChange,
  onFilterSelect,
  onTicketSelection,
  onTicketClick,
  onTicketAction,
  onApprove,
  onReject,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Show different table headers based on active view */}
      {activeView !== "myTickets" && (
        <div className="px-3 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-base font-medium text-gray-900">
              {activeView === "all" && "All Tickets"}
              {activeView === "unapproved" && "Unapproved Tickets"} (
              {currentViewTickets.length})
            </h3>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                onChange={(e) => onSelectAllTickets(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="text-xs text-gray-600">Select All</label>
            </div>
          </div>
        </div>
      )}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <SuperUserTableHeaders
                activeView={activeView}
                currentViewTickets={currentViewTickets}
                activeFilterColumn={activeFilterColumn}
                pendingFilters={pendingFilters}
                onFilterColumnChange={onFilterColumnChange}
                onFilterSelect={onFilterSelect}
              />
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentViewTickets.map((ticket) => (
              <SuperUserTableRow
                key={ticket.id}
                ticket={ticket}
                activeView={activeView}
                isSelected={selectedTickets.includes(ticket.id)}
                onTicketSelection={onTicketSelection}
                onTicketClick={onTicketClick}
                onTicketAction={onTicketAction}
                onApprove={onApprove}
                onReject={onReject}
              />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SuperUserTableContainer;
