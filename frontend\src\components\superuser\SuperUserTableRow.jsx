import {
  ArrowUp,
  Calendar,
  CheckCircle,
  Clock,
  Eye,
  Trash2,
  Users,
  XCircle,
} from "lucide-react";
import {
  getPriorityColor,
  getRiskColor,
  getStatusColor,
  getCategoryColor,
} from "./utils/superUserHelpers";

const SuperUserTableRow = ({
  ticket,
  activeView,
  isSelected,
  onTicketSelection,
  onTicketClick,
  onTicketAction,
  onApprove,
  onReject,
}) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case "Pending Approval":
        return <Clock className="w-3 h-3 text-yellow-500" />;
      case "In Progress":
        return <Eye className="w-3 h-3 text-blue-500" />;
      case "Resolved":
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case "Rejected":
        return <XCircle className="w-3 h-3 text-red-500" />;
      default:
        return <Clock className="w-3 h-3 text-gray-500" />;
    }
  };

  const handleRowClick = (e) => {
    // Don't trigger row click if clicking on checkbox or action buttons
    if (e.target.type !== "checkbox" && !e.target.closest("button")) {
      onTicketClick(ticket);
    }
  };

  const renderMyTicketsRow = () => (
    <>
      {/* Customer Dashboard style rows for My Tickets */}
      <td className="px-2 py-2 whitespace-nowrap">
        <div>
          <div className="text-xs font-medium text-gray-900">{ticket.id}</div>
          <div className="text-xs text-gray-500">{ticket.subject}</div>
          <div className="text-xs text-gray-400">{ticket.category}</div>
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap">
        <div className="flex items-center">
          {getStatusIcon(ticket.status)}
          <span
            className={`ml-1 px-1.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(
              ticket.status
            )}`}
          >
            {ticket.status}
          </span>
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap">
        <div className="flex items-center">
          <div
            className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
              ticket.priority
            )}`}
          ></div>
          <span className="text-xs text-gray-900">{ticket.priority}</span>
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-900">
        {ticket.assignedAgent || "Unassigned"}
      </td>
      <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
        {ticket.lastUpdate}
      </td>
      <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
        <div className="flex space-x-1">
          <button
            className="text-blue-600 hover:text-blue-900 p-1"
            title="View Details"
            onClick={(e) => {
              e.stopPropagation();
              onTicketClick(ticket);
            }}
          >
            <Eye className="w-3 h-3" />
          </button>
          <button
            className="text-red-600 hover:text-red-900 p-1"
            title="Delete Ticket"
            onClick={(e) => {
              e.stopPropagation();
              onTicketAction("delete", ticket);
            }}
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </td>
    </>
  );

  const renderSuperUserRow = () => (
    <>
      {/* Original Super User rows for All Tickets and Unapproved Tickets */}
      <td className="px-2 py-2 whitespace-nowrap">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onTicketSelection(ticket.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          onClick={(e) => e.stopPropagation()}
        />
      </td>
      <td className="px-2 py-2">
        <div>
          <div className="text-xs font-medium text-gray-900">{ticket.id}</div>
          <div className="text-xs text-gray-600">{ticket.subject}</div>
          {ticket.attachments > 0 && (
            <div className="text-xs text-gray-400 flex items-center mt-0.5">
              <span className="flex items-center">
                📎 {ticket.attachments} attachment
                {ticket.attachments > 1 ? "s" : ""}
              </span>
            </div>
          )}
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-6 w-6">
            <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
              <Users className="w-3 h-3 text-gray-600" />
            </div>
          </div>
          <div className="ml-2">
            <div className="text-xs font-medium text-gray-900">
              {ticket.customer}
            </div>
          </div>
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap">
        <span
          className={`px-2 py-1 text-xs font-medium rounded-md border ${getCategoryColor(
            ticket.category
          )}`}
        >
          {ticket.category}
        </span>
      </td>
      <td className="px-2 py-2 whitespace-nowrap">
        <div className="flex items-center">
          <div
            className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
              ticket.priority
            )}`}
          ></div>
          <span className="text-xs text-gray-900">{ticket.priority}</span>
          {ticket.priority === "Critical" && (
            <ArrowUp className="w-3 h-3 ml-1 text-red-500" />
          )}
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap">
        <span
          className={`px-1.5 py-0.5 text-xs font-medium rounded-full ${getRiskColor(
            ticket.riskLevel
          )}`}
        >
          {ticket.riskLevel}
        </span>
      </td>
      <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
        <div className="flex items-center">
          <Calendar className="w-3 h-3 mr-1" />
          {ticket.created}
        </div>
      </td>
      <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
        <div className="flex space-x-1">
          {/* Always show View Details button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onTicketClick(ticket);
            }}
            className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100 transition-colors"
            title="View Details"
          >
            <Eye className="w-3 h-3" />
          </button>

          {/* Show Approve/Reject buttons only in "Unapproved Tickets" tab */}
          {activeView === "unapproved" && (
            <>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onApprove(ticket.id, "");
                }}
                className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100 transition-colors"
                title="Quick Approve"
              >
                <CheckCircle className="w-3 h-3" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  // For quick reject, we'll need a reason, so open the modal
                  onTicketClick(ticket);
                }}
                className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition-colors"
                title="Reject (with reason)"
              >
                <XCircle className="w-3 h-3" />
              </button>
            </>
          )}

          {/* Show Delete button only in "All Tickets" tab */}
          {activeView === "all" && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onTicketAction("delete", ticket);
              }}
              className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition-colors"
              title="Delete Ticket"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          )}
        </div>
      </td>
    </>
  );

  return (
    <tr
      className="hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={handleRowClick}
    >
      {activeView === "myTickets" ? renderMyTicketsRow() : renderSuperUserRow()}
    </tr>
  );
};

export default SuperUserTableRow;
